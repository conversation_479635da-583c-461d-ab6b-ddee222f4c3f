"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { ReferencesService, ReferenceItem } from "@/Firebase/firestore/services/ReferencesService";
import { auth } from "@/Firebase/Authentication/authConfig";
import { Loader2, Plus, Link as LinkIcon, Hash, FileText } from "lucide-react";

interface AddReferenceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReferenceAdded: (reference: ReferenceItem) => void;
  isRTL?: boolean;
}

interface FormData {
  title: string;
  link: string;
  pageNumber: string;
  details: string;
}

interface FormErrors {
  title?: string;
  link?: string;
  pageNumber?: string;
  details?: string;
}

export function AddReferenceModal({ 
  isOpen, 
  onClose, 
  onReferenceAdded, 
  isRTL = false 
}: AddReferenceModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    title: "",
    link: "",
    pageNumber: "",
    details: ""
  });
  const [errors, setErrors] = useState<FormErrors>({});

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Title is required
    if (!formData.title.trim()) {
      newErrors.title = isRTL ? "العنوان مطلوب" : "Title is required";
    }

    // Validate URL format if link is provided
    if (formData.link.trim()) {
      try {
        new URL(formData.link);
      } catch {
        newErrors.link = isRTL ? "رابط غير صحيح" : "Invalid URL format";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error("User not authenticated");
      }

      const referenceData = {
        title: formData.title.trim(),
        ...(formData.link.trim() && { link: formData.link.trim() }),
        ...(formData.pageNumber.trim() && { pageNumber: formData.pageNumber.trim() }),
        ...(formData.details.trim() && { details: formData.details.trim() }),
        createdBy: currentUser.uid
      };

      const referenceId = await ReferencesService.createReference(referenceData);
      
      // Create the reference object to pass back
      const newReference: ReferenceItem = {
        id: referenceId,
        ...referenceData,
        createdAt: new Date() as any,
        updatedAt: new Date() as any
      };

      onReferenceAdded(newReference);
      
      toast({
        title: isRTL ? "تم إضافة المرجع بنجاح" : "Reference added successfully",
        variant: "default",
      });

      // Reset form
      setFormData({
        title: "",
        link: "",
        pageNumber: "",
        details: ""
      });
      setErrors({});
      onClose();
    } catch (error) {
      console.error("Error adding reference:", error);
      toast({
        title: isRTL ? "خطأ في إضافة المرجع" : "Error adding reference",
        description: isRTL ? "حدث خطأ أثناء إضافة المرجع" : "An error occurred while adding the reference",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        title: "",
        link: "",
        pageNumber: "",
        details: ""
      });
      setErrors({});
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={`sm:max-w-[500px] ${isRTL ? 'rtl' : 'ltr'} border-[var(--brand-blue)]/20`}>
        <DialogHeader className="bg-gradient-to-r from-[var(--brand-blue)]/5 to-transparent p-6 -m-6 mb-4 rounded-t-lg">
          <DialogTitle className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)]`}>
            <Plus size={20} className="text-[var(--brand-blue)]" />
            {isRTL ? "إضافة مرجع جديد" : "Add New Reference"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title Field */}
          <div className="space-y-2">
            <Label htmlFor="title" className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)] font-medium`}>
              <FileText size={16} className="text-[var(--brand-blue)]" />
              {isRTL ? "العنوان" : "Title"} <span className="text-red-500">*</span>
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              placeholder={isRTL ? "أدخل عنوان المرجع" : "Enter reference title"}
              className={`${isRTL ? 'text-right' : 'text-left'} ${errors.title ? 'border-red-500' : 'border-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]/20'}`}
              disabled={isLoading}
            />
            {errors.title && (
              <p className={`text-sm text-red-500 ${isRTL ? 'text-right' : 'text-left'}`}>
                {errors.title}
              </p>
            )}
          </div>

          {/* Link Field */}
          <div className="space-y-2">
            <Label htmlFor="link" className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)] font-medium`}>
              <LinkIcon size={16} className="text-[var(--brand-blue)]" />
              {isRTL ? "الرابط (اختياري)" : "Link (Optional)"}
            </Label>
            <Input
              id="link"
              type="url"
              value={formData.link}
              onChange={(e) => handleInputChange("link", e.target.value)}
              placeholder={isRTL ? "https://example.com" : "https://example.com"}
              className={`${isRTL ? 'text-right' : 'text-left'} ${errors.link ? 'border-red-500' : 'border-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]/20'}`}
              disabled={isLoading}
            />
            {errors.link && (
              <p className={`text-sm text-red-500 ${isRTL ? 'text-right' : 'text-left'}`}>
                {errors.link}
              </p>
            )}
          </div>

          {/* Page Number Field */}
          <div className="space-y-2">
            <Label htmlFor="pageNumber" className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)] font-medium`}>
              <Hash size={16} className="text-[var(--brand-blue)]" />
              {isRTL ? "رقم الصفحة (اختياري)" : "Page Number (Optional)"}
            </Label>
            <Input
              id="pageNumber"
              value={formData.pageNumber}
              onChange={(e) => handleInputChange("pageNumber", e.target.value)}
              placeholder={isRTL ? "مثال: ص 25 أو 25-30" : "e.g., p. 25 or 25-30"}
              className={`${isRTL ? 'text-right' : 'text-left'} border-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]/20`}
              disabled={isLoading}
            />
          </div>

          {/* Details Field */}
          <div className="space-y-2">
            <Label htmlFor="details" className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)] font-medium`}>
              <FileText size={16} className="text-[var(--brand-blue)]" />
              {isRTL ? "التفاصيل (اختياري)" : "Details (Optional)"}
            </Label>
            <Textarea
              id="details"
              value={formData.details}
              onChange={(e) => handleInputChange("details", e.target.value)}
              placeholder={isRTL ? "أضف أي تفاصيل إضافية حول المرجع" : "Add any additional details about the reference"}
              className={`${isRTL ? 'text-right' : 'text-left'} min-h-[80px] border-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]/20`}
              disabled={isLoading}
            />
          </div>

          {/* Action Buttons */}
          <div className={`flex gap-3 pt-4 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
            >
              {isLoading ? (
                <>
                  <Loader2 className={`h-4 w-4 animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {isRTL ? "جاري الإضافة..." : "Adding..."}
                </>
              ) : (
                <>
                  <Plus className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {isRTL ? "إضافة المرجع" : "Add Reference"}
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1 border-[var(--brand-blue)]/30 text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10"
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
