/**
 * ReferencesService - Manages reference items
 * 
 * This service provides comprehensive functionality for managing reference items,
 * including CRUD operations for references with title, link, page number, and details.
 * 
 * Features:
 * - Create, read, update, delete reference items
 * - Support for optional links and page numbers
 * - Timestamp tracking for creation and updates
 * - Query and filtering capabilities
 */

import {
  collection,
  doc,
  addDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  Timestamp,
  where,
  getDoc,
  CollectionReference
} from 'firebase/firestore';
import { firestore as db } from '../firestoreConfig';

export interface ReferenceItem {
  id?: string;
  title: string;
  link?: string; // Optional URL link
  pageNumber?: string; // Optional page number
  details?: string; // Optional additional details
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy?: string; // User ID who created the reference
}

// Collection reference
const referencesCollection = collection(db, 'references') as CollectionReference<ReferenceItem>;

export class ReferencesService {
  /**
   * Creates a new reference item
   */
  static async createReference(referenceData: Omit<ReferenceItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const newReference = {
        ...referenceData,
        createdAt: now,
        updatedAt: now
      };

      // Remove undefined fields to prevent Firebase errors
      const cleanedReference = Object.fromEntries(
        Object.entries(newReference).filter(([, value]) => value !== undefined && value !== '')
      );

      const docRef = await addDoc(referencesCollection, cleanedReference);
      return docRef.id;
    } catch (error) {
      console.error('Error creating reference:', error);
      throw new Error('Failed to create reference');
    }
  }

  /**
   * Gets all reference items
   */
  static async getAllReferences(): Promise<ReferenceItem[]> {
    try {
      const q = query(referencesCollection, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching references:', error);
      throw new Error('Failed to fetch references');
    }
  }

  /**
   * Gets a specific reference by ID
   */
  static async getReferenceById(referenceId: string): Promise<ReferenceItem | null> {
    try {
      const docRef = doc(referencesCollection, referenceId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching reference:', error);
      throw new Error('Failed to fetch reference');
    }
  }

  /**
   * Updates an existing reference
   */
  static async updateReference(referenceId: string, updateData: Partial<Omit<ReferenceItem, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> {
    try {
      const docRef = doc(referencesCollection, referenceId);
      const now = Timestamp.now();
      
      // Remove undefined fields and empty strings
      const cleanedUpdateData = Object.fromEntries(
        Object.entries(updateData).filter(([, value]) => value !== undefined && value !== '')
      );

      await updateDoc(docRef, {
        ...cleanedUpdateData,
        updatedAt: now
      });
    } catch (error) {
      console.error('Error updating reference:', error);
      throw new Error('Failed to update reference');
    }
  }

  /**
   * Deletes a reference
   */
  static async deleteReference(referenceId: string): Promise<void> {
    try {
      const docRef = doc(referencesCollection, referenceId);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting reference:', error);
      throw new Error('Failed to delete reference');
    }
  }

  /**
   * Gets references created by a specific user
   */
  static async getReferencesByUser(userId: string): Promise<ReferenceItem[]> {
    try {
      const q = query(
        referencesCollection, 
        where('createdBy', '==', userId),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching user references:', error);
      throw new Error('Failed to fetch user references');
    }
  }

  /**
   * Searches references by title
   */
  static async searchReferencesByTitle(searchTerm: string): Promise<ReferenceItem[]> {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation that gets all references and filters client-side
      // For production, consider using Algolia or similar service for better search
      const allReferences = await this.getAllReferences();
      
      return allReferences.filter(reference => 
        reference.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching references:', error);
      throw new Error('Failed to search references');
    }
  }
}
