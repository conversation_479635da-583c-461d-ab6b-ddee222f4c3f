"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { ReferencesService, ReferenceItem } from "@/Firebase/firestore/services/ReferencesService";
import { AddReferenceModal } from "@/components/ui/AddReferenceModal";
import { ReferencesList } from "@/components/ui/ReferencesList";
import { 
  Plus, 
  Search, 
  BookOpen, 
  Loader2,
  RefreshCw
} from "lucide-react";

interface ReferencesTabProps {
  isRTL?: boolean;
  onStatsUpdate?: () => void;
}

export function ReferencesTab({ isRTL = false, onStatsUpdate }: ReferencesTabProps) {
  const { toast } = useToast();
  const [references, setReferences] = useState<ReferenceItem[]>([]);
  const [filteredReferences, setFilteredReferences] = useState<ReferenceItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Load references on component mount
  useEffect(() => {
    loadReferences();
  }, [loadReferences]);

  // Filter references based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredReferences(references);
    } else {
      const filtered = references.filter(reference =>
        reference.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (reference.details && reference.details.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (reference.pageNumber && reference.pageNumber.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredReferences(filtered);
    }
  }, [references, searchTerm]);

  const loadReferences = useCallback(async () => {
    try {
      setIsLoading(true);
      const fetchedReferences = await ReferencesService.getAllReferences();
      setReferences(fetchedReferences);
    } catch (error) {
      console.error("Error loading references:", error);
      toast({
        title: isRTL ? "خطأ في تحميل المراجع" : "Error loading references",
        description: isRTL ? "حدث خطأ أثناء تحميل المراجع" : "An error occurred while loading references",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isRTL, toast]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadReferences();
    setIsRefreshing(false);
    toast({
      title: isRTL ? "تم تحديث المراجع" : "References refreshed",
      variant: "default",
    });
  };

  const handleReferenceAdded = (newReference: ReferenceItem) => {
    setReferences(prev => [newReference, ...prev]);
    onStatsUpdate?.();
  };

  const handleReferenceUpdated = (updatedReference: ReferenceItem) => {
    setReferences(prev => 
      prev.map(ref => ref.id === updatedReference.id ? updatedReference : ref)
    );
  };

  const handleReferenceDeleted = (referenceId: string) => {
    setReferences(prev => prev.filter(ref => ref.id !== referenceId));
    onStatsUpdate?.();
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <Card className="border-[var(--brand-blue)]/20 shadow-lg">
        <CardHeader className="bg-white border-b border-[var(--brand-blue)]/10">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
            <CardTitle className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : 'flex-row'} text-[var(--brand-dark-gray)]`}>
              <BookOpen size={24} className="text-[var(--brand-blue)]" />
              <span>{isRTL ? "إدارة المراجع" : "References Management"}</span>
            </CardTitle>
            <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
              <Button
                onClick={handleRefresh}
                variant="outline"
                size="sm"
                disabled={isRefreshing}
                className="h-9 border-[var(--brand-blue)]/30 text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10"
              >
                <RefreshCw size={16} className={`${isRefreshing ? 'animate-spin' : ''} ${isRTL ? 'ml-2' : 'mr-2'}`} />
                {isRTL ? "تحديث" : "Refresh"}
              </Button>
              <Button
                onClick={() => setIsAddModalOpen(true)}
                className="h-9 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
              >
                <Plus size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {isRTL ? "إضافة مرجع" : "Add Reference"}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="bg-white">
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search size={20} className={`absolute top-1/2 transform -translate-y-1/2 text-[var(--brand-blue)]/60 ${isRTL ? 'right-3' : 'left-3'}`} />
              <Input
                placeholder={isRTL ? "البحث في المراجع..." : "Search references..."}
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className={`${isRTL ? 'pr-10 text-right' : 'pl-10 text-left'} border-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]/20`}
              />
            </div>

            {/* Stats */}
            <div className={`flex items-center gap-6 text-sm ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-[var(--brand-blue)]"></div>
                <span className="text-[var(--brand-dark-gray)] font-medium">
                  {isRTL
                    ? `إجمالي المراجع: ${references.length}`
                    : `Total References: ${references.length}`
                  }
                </span>
              </div>
              {searchTerm && (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span className="text-[var(--brand-dark-gray)] font-medium">
                    {isRTL
                      ? `نتائج البحث: ${filteredReferences.length}`
                      : `Search Results: ${filteredReferences.length}`
                    }
                  </span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      <Card className="border-[var(--brand-blue)]/20 shadow-lg">
        <CardContent className="p-6 bg-white">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 size={48} className="mx-auto text-[var(--brand-blue)] mb-4 animate-spin" />
                <p className="text-[var(--brand-dark-gray)] font-medium">
                  {isRTL ? "جاري تحميل المراجع..." : "Loading references..."}
                </p>
              </div>
            </div>
          ) : (
            <ReferencesList
              references={filteredReferences}
              onReferenceUpdated={handleReferenceUpdated}
              onReferenceDeleted={handleReferenceDeleted}
              isRTL={isRTL}
            />
          )}
        </CardContent>
      </Card>

      {/* Add Reference Modal */}
      <AddReferenceModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onReferenceAdded={handleReferenceAdded}
        isRTL={isRTL}
      />
    </div>
  );
}
