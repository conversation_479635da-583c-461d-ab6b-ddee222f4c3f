"use client";

import React, { useState, useEffect } from "react";
import { Locale } from '@/i18n-config';
import { ReferencesTab } from '@/components/References/ReferencesTab';
import { ReferencesService } from '@/Firebase/firestore/services/ReferencesService';
import { motion } from "framer-motion";
import { BookOpen, Plus, Search, FileText, Link as LinkIcon, Hash } from "lucide-react";

interface ReferencesPageClientProps {
  lang: Locale;
}

export function ReferencesPageClient({ lang }: ReferencesPageClientProps) {
  const isRTL = lang === 'ar';
  const [stats, setStats] = useState({
    total: 0,
    withLinks: 0,
    withPages: 0,
    withDetails: 0
  });

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const references = await ReferencesService.getAllReferences();
      setStats({
        total: references.length,
        withLinks: references.filter(ref => ref.link).length,
        withPages: references.filter(ref => ref.pageNumber).length,
        withDetails: references.filter(ref => ref.details).length
      });
    } catch (error) {
      console.error("Error loading stats:", error);
    }
  };

  const statsData = [
    {
      icon: <BookOpen size={24} />,
      value: stats.total,
      label: isRTL ? "إجمالي المراجع" : "Total References",
      color: "text-white"
    },
    {
      icon: <LinkIcon size={24} />,
      value: stats.withLinks,
      label: isRTL ? "مع روابط" : "With Links",
      color: "text-white"
    },
    {
      icon: <Hash size={24} />,
      value: stats.withPages,
      label: isRTL ? "مع أرقام صفحات" : "With Page Numbers",
      color: "text-white"
    },
    {
      icon: <FileText size={24} />,
      value: stats.withDetails,
      label: isRTL ? "مع تفاصيل" : "With Details",
      color: "text-white"
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Hero Section - Full Width */}
      <div className="relative min-h-[85vh] bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
          <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-white rounded-full opacity-50"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center min-h-[85vh] px-8">
          <div className="max-w-7xl mx-auto w-full">
            {/* Main Title */}
            <div className={`text-center mb-16 ${isRTL ? 'rtl' : 'ltr'}`}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="flex justify-center mb-6"
              >
                <div className="p-4 rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30">
                  <BookOpen size={48} className="text-white" />
                </div>
              </motion.div>
              
              <div>
                <motion.h1 
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="text-5xl md:text-7xl font-bold text-white mb-2 tracking-tight"
                >
                  {isRTL ? "المراجع" : "References"}
                </motion.h1>
                <motion.p 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="text-xl md:text-2xl text-white/90 font-medium"
                >
                  {isRTL ? "إدارة المراجع والمصادر المؤسسية" : "Enterprise References & Resources Management"}
                </motion.p>
              </div>
            </div>

            {/* Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8 max-w-5xl mx-auto"
            >
              {statsData.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
                  className="bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 group"
                >
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className="p-3 rounded-xl bg-white/20 text-white group-hover:bg-white/30 transition-all duration-300">
                        {stat.icon}
                      </div>
                    </div>
                    <div className="text-3xl font-bold text-white mb-2">
                      {stat.value}
                    </div>
                    <div className="text-white/80 text-sm font-medium">
                      {stat.label}
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-8 py-8">
        <div className="max-w-7xl mx-auto">
          <ReferencesTab isRTL={isRTL} onStatsUpdate={loadStats} />
        </div>
      </div>
    </div>
  );
}
