"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { ReferencesService, ReferenceItem } from "@/Firebase/firestore/services/ReferencesService";
import { Loader2, Save, Link as LinkIcon, Hash, FileText, Edit } from "lucide-react";

interface EditReferenceModalProps {
  isOpen: boolean;
  onClose: () => void;
  reference: ReferenceItem | null;
  onReferenceUpdated: (reference: ReferenceItem) => void;
  isRTL?: boolean;
}

interface FormData {
  title: string;
  link: string;
  pageNumber: string;
  details: string;
}

interface FormErrors {
  title?: string;
  link?: string;
  pageNumber?: string;
  details?: string;
}

export function EditReferenceModal({ 
  isOpen, 
  onClose, 
  reference,
  onReferenceUpdated, 
  isRTL = false 
}: EditReferenceModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    title: "",
    link: "",
    pageNumber: "",
    details: ""
  });
  const [errors, setErrors] = useState<FormErrors>({});

  // Update form data when reference changes
  useEffect(() => {
    if (reference) {
      setFormData({
        title: reference.title || "",
        link: reference.link || "",
        pageNumber: reference.pageNumber || "",
        details: reference.details || ""
      });
    }
  }, [reference]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Title is required
    if (!formData.title.trim()) {
      newErrors.title = isRTL ? "العنوان مطلوب" : "Title is required";
    }

    // Validate URL format if link is provided
    if (formData.link.trim()) {
      try {
        new URL(formData.link);
      } catch {
        newErrors.link = isRTL ? "رابط غير صحيح" : "Invalid URL format";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reference?.id || !validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const updateData = {
        title: formData.title.trim(),
        ...(formData.link.trim() && { link: formData.link.trim() }),
        ...(formData.pageNumber.trim() && { pageNumber: formData.pageNumber.trim() }),
        ...(formData.details.trim() && { details: formData.details.trim() })
      };

      await ReferencesService.updateReference(reference.id, updateData);
      
      // Create the updated reference object to pass back
      const updatedReference: ReferenceItem = {
        ...reference,
        ...updateData,
        updatedAt: new Date() as any
      };

      onReferenceUpdated(updatedReference);
      
      toast({
        title: isRTL ? "تم تحديث المرجع بنجاح" : "Reference updated successfully",
        variant: "default",
      });

      onClose();
    } catch (error) {
      console.error("Error updating reference:", error);
      toast({
        title: isRTL ? "خطأ في تحديث المرجع" : "Error updating reference",
        description: isRTL ? "حدث خطأ أثناء تحديث المرجع" : "An error occurred while updating the reference",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setErrors({});
      onClose();
    }
  };

  if (!reference) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={`sm:max-w-[500px] ${isRTL ? 'rtl' : 'ltr'} border-[var(--brand-blue)]/20`}>
        <DialogHeader className="bg-gradient-to-r from-[var(--brand-blue)]/5 to-transparent p-6 -m-6 mb-4 rounded-t-lg">
          <DialogTitle className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)]`}>
            <Edit size={20} className="text-[var(--brand-blue)]" />
            {isRTL ? "تعديل المرجع" : "Edit Reference"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title Field */}
          <div className="space-y-2">
            <Label htmlFor="edit-title" className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)] font-medium`}>
              <FileText size={16} className="text-[var(--brand-blue)]" />
              {isRTL ? "العنوان" : "Title"} <span className="text-red-500">*</span>
            </Label>
            <Input
              id="edit-title"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              placeholder={isRTL ? "أدخل عنوان المرجع" : "Enter reference title"}
              className={`${isRTL ? 'text-right' : 'text-left'} ${errors.title ? 'border-red-500' : 'border-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]/20'}`}
              disabled={isLoading}
            />
            {errors.title && (
              <p className={`text-sm text-red-500 ${isRTL ? 'text-right' : 'text-left'}`}>
                {errors.title}
              </p>
            )}
          </div>

          {/* Link Field */}
          <div className="space-y-2">
            <Label htmlFor="edit-link" className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)] font-medium`}>
              <LinkIcon size={16} className="text-[var(--brand-blue)]" />
              {isRTL ? "الرابط (اختياري)" : "Link (Optional)"}
            </Label>
            <Input
              id="edit-link"
              type="url"
              value={formData.link}
              onChange={(e) => handleInputChange("link", e.target.value)}
              placeholder={isRTL ? "https://example.com" : "https://example.com"}
              className={`${isRTL ? 'text-right' : 'text-left'} ${errors.link ? 'border-red-500' : 'border-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]/20'}`}
              disabled={isLoading}
            />
            {errors.link && (
              <p className={`text-sm text-red-500 ${isRTL ? 'text-right' : 'text-left'}`}>
                {errors.link}
              </p>
            )}
          </div>

          {/* Page Number Field */}
          <div className="space-y-2">
            <Label htmlFor="edit-pageNumber" className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)] font-medium`}>
              <Hash size={16} className="text-[var(--brand-blue)]" />
              {isRTL ? "رقم الصفحة (اختياري)" : "Page Number (Optional)"}
            </Label>
            <Input
              id="edit-pageNumber"
              value={formData.pageNumber}
              onChange={(e) => handleInputChange("pageNumber", e.target.value)}
              placeholder={isRTL ? "مثال: ص 25 أو 25-30" : "e.g., p. 25 or 25-30"}
              className={`${isRTL ? 'text-right' : 'text-left'} border-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]/20`}
              disabled={isLoading}
            />
          </div>

          {/* Details Field */}
          <div className="space-y-2">
            <Label htmlFor="edit-details" className={`flex items-center gap-2 ${isRTL ? 'text-right flex-row-reverse' : 'text-left'} text-[var(--brand-dark-gray)] font-medium`}>
              <FileText size={16} className="text-[var(--brand-blue)]" />
              {isRTL ? "التفاصيل (اختياري)" : "Details (Optional)"}
            </Label>
            <Textarea
              id="edit-details"
              value={formData.details}
              onChange={(e) => handleInputChange("details", e.target.value)}
              placeholder={isRTL ? "أضف أي تفاصيل إضافية حول المرجع" : "Add any additional details about the reference"}
              className={`${isRTL ? 'text-right' : 'text-left'} min-h-[80px] border-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]/20`}
              disabled={isLoading}
            />
          </div>

          {/* Action Buttons */}
          <div className={`flex gap-3 pt-4 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
            >
              {isLoading ? (
                <>
                  <Loader2 className={`h-4 w-4 animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {isRTL ? "جاري التحديث..." : "Updating..."}
                </>
              ) : (
                <>
                  <Save className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {isRTL ? "حفظ التغييرات" : "Save Changes"}
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1 border-[var(--brand-blue)]/30 text-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/10"
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
